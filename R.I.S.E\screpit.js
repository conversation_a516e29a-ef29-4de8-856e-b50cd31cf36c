const words = ['RESEARCH', 'INNOVATION', 'SOLUTIONS', 'EXCELLENCE','🖤'];
let index = 0;
const wordBox = document.getElementById("wordBox");
const dynamicBox = document.querySelector('.dynamic-word-box');

function updateWord() {
    wordBox.style.transform = 'translateX(-100%)';
    wordBox.style.opacity = 0;
  
    setTimeout(() => {
      index = (index + 1) % words.length;
      wordBox.textContent = words[index];
  
      const textWidth = wordBox.offsetWidth;
      dynamicBox.style.width = textWidth + 24 + 'px';
  
      wordBox.style.transform = 'translateX(100%)';
  
      requestAnimationFrame(() => {
        wordBox.style.transform = 'translateX(0)';
        wordBox.style.opacity = 1;
      });
    }, 400);
  }

window.addEventListener('load', () => {
  const textWidth = wordBox.offsetWidth;
  dynamicBox.style.width = textWidth + 20 + 'px';
});

setInterval(update<PERSON><PERSON>, 2000);