html,body {
    margin: 0;
    background-color: #121212;     
  }
  
  .top-section {
    width: 197vh;              
    height: 3vh;              
    background-color: #1F1F1F; 
    color: #F8FAFC;  
    font-family: 'Trebuchet MS', 'Lucida Sans Unicode', 'Lucida Grande', 'Lucida Sans', Arial, sans-serif; 
    font-size: 20px;         
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 2rem;
    box-shadow: 2px 2px 4px 4px rgba(107, 107, 107, 0.3); 
    position: relative;   
    border-radius: 0 0 20px 20px;     
  }

  .image {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 50%;
  }

  .static-word {
    font-size: 25px;
    margin-right: 11px;
    margin-top: -10px;
    font-weight: bold;

  }

  .dynamic-word-box {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: #E5E7E8;
    border-radius: 8px;
    border: 1px solid #1F1F1F;
    overflow: hidden;
    position: relative;
    margin-top: -10px;
    padding: 6px 12px;   
    min-width: 60px;     
    max-width: 180px;   
    box-sizing: border-box;
    transition: width 0.3s ease;
    white-space: nowrap;
    color: #121212;
  }

#wordBox {
  display: inline-block;
  transition: transform 0.4s ease, opacity 0.4s ease;
  will-change: transform, opacity;}

  